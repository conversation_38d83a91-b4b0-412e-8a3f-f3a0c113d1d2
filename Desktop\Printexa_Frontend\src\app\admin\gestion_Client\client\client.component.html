<section class="content">
  <div class="content-block">
    <div class="block-header">
      <!-- breadcrumb -->
      <app-breadcrumb [title]="breadscrums[0].title" [items]="breadscrums[0].items" [active_item]="breadscrums[0].active"></app-breadcrumb>
    </div>
    <div class="row">
      <div class="col-xs-12 col-sm-12 col-md-12 col-lg-12">
        <div class="card">
          <div class="header">
            <h2>
              <strong>Liste des Clients</strong>
            </h2>
            <div class="header-dropdown">
              <button mat-icon-button class="example-icon favorite-icon" aria-label="refresh" (click)="refresh()">
                <mat-icon>refresh</mat-icon>
              </button>
            </div>
          </div>
          <div class="body">
            <div class="responsive_table">
              <div class="table-responsive">
                <div class="table-header">
                  <div class="add-new">
                    <button mat-flat-button color="primary" class="btn-space" (click)="addNew()">
                      <mat-icon>add</mat-icon>
                      Ajouter Client
                    </button>
                    @if (selection.selected.length > 0) {
                    <button mat-flat-button color="warn" class="btn-space" (click)="removeSelectedRows()">
                      <mat-icon>delete</mat-icon>
                      Supprimer Sélection
                    </button>
                    }
                  </div>
                  <div class="table-search">
                    <mat-form-field class="search-form-field" floatLabel="auto">
                      <input matInput #filter placeholder="Rechercher..." autocomplete="off">
                      <button mat-icon-button matSuffix mat-flat-button aria-label="search" class="search-icon">
                        <mat-icon>search</mat-icon>
                      </button>
                    </mat-form-field>
                  </div>
                  <div class="table-actions">
                    <button mat-icon-button color="primary" class="btn-space" matTooltip="Exporter Excel" (click)="exportExcel()">
                      <mat-icon>get_app</mat-icon>
                    </button>
                  </div>
                </div>

                <table mat-table [dataSource]="dataSource" class="mat-elevation-z8 data-table" matSort #table>
                  <!-- Checkbox Column -->
                  <ng-container matColumnDef="select">
                    <th mat-header-cell *matHeaderCellDef>
                      <mat-checkbox (change)="$event ? masterToggle() : null" [checked]="selection.hasValue() && isAllSelected()" [indeterminate]="selection.hasValue() && !isAllSelected()">
                      </mat-checkbox>
                    </th>
                    <td mat-cell *matCellDef="let row">
                      <mat-checkbox (click)="$event.stopPropagation()" (change)="$event ? selection.toggle(row) : null" [checked]="selection.isSelected(row)">
                      </mat-checkbox>
                    </td>
                  </ng-container>

                  <!-- Code Column -->
                  <ng-container matColumnDef="code">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Code Client</th>
                    <td mat-cell *matCellDef="let row" class="client-code">
                      <strong>{{row.code}}</strong>
                    </td>
                  </ng-container>

                  <!-- Syntax Column -->
                  <ng-container matColumnDef="syntax">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Raison Sociale</th>
                    <td mat-cell *matCellDef="let row" class="client-syntax">
                      {{row.syntax || 'Non défini'}}
                    </td>
                  </ng-container>

                  <!-- MatFiscal Column -->
                  <ng-container matColumnDef="matFiscal">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Matricule Fiscal</th>
                    <td mat-cell *matCellDef="let row" class="client-matfiscal">
                      {{row.matFiscal || 'Non défini'}}
                    </td>
                  </ng-container>

                  <!-- Email Column -->
                  <ng-container matColumnDef="email">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Email</th>
                    <td mat-cell *matCellDef="let row" class="client-email">
                      <a *ngIf="row.email" [href]="'mailto:' + row.email" class="email-link">
                        {{row.email}}
                      </a>
                      <span *ngIf="!row.email" class="no-data">Non défini</span>
                    </td>
                  </ng-container>

                  <!-- Telephone Column -->
                  <ng-container matColumnDef="telephone">
                    <th mat-header-cell *matHeaderCellDef mat-sort-header>Téléphone</th>
                    <td mat-cell *matCellDef="let row" class="client-telephone">
                      <a *ngIf="row.telephone" [href]="'tel:' + row.telephone" class="phone-link">
                        {{row.telephone}}
                      </a>
                      <span *ngIf="!row.telephone" class="no-data">Non défini</span>
                    </td>
                  </ng-container>

                  <!-- Actions Column -->
                  <ng-container matColumnDef="actions">
                    <th mat-header-cell *matHeaderCellDef class="pr-0">Actions</th>
                    <td mat-cell *matCellDef="let row" class="action-link">
                      <button mat-icon-button color="accent" class="btn-tbl-edit" matTooltip="Modifier" (click)="editCall(row)">
                        <mat-icon>edit</mat-icon>
                      </button>
                      <button mat-icon-button color="warn" class="btn-tbl-delete" matTooltip="Supprimer" (click)="deleteItem(row)">
                        <mat-icon>delete</mat-icon>
                      </button>
                    </td>
                  </ng-container>

                  <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
                  <tr mat-row *matRowDef="let row; columns: displayedColumns;" [ngClass]="{'selected': selection.isSelected(row)}" (click)="selection.toggle(row)"></tr>
                </table>

                <mat-paginator #paginator [length]="dataSource.filteredData.length" [pageIndex]="0" [pageSize]="10" [pageSizeOptions]="[5, 10, 25, 100]" showFirstLastButtons>
                </mat-paginator>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</section>
